"""
Custom indicators based on Excel formulas
"""

import logging
import polars as pl
from typing import Dict, Any, List
from ..core.config_manager import CustomIndicatorConfig
from ..models.schemas import OutputSchemaManager


logger = logging.getLogger(__name__)


class CustomIndicators:
    """Calculate custom indicators based on Excel formulas"""
    
    def __init__(self, config: CustomIndicatorConfig):
        self.config = config

        # Default thresholds from Master sheet references
        self.thresholds = config.thresholds
    
    def calculate_all(self, df: pl.DataFrame) -> pl.DataFrame:
        """Calculate all enabled custom indicators"""
        if df.is_empty():
            return df

        logger.debug(f"Starting custom indicators calculation with {len(df)} rows")
        logger.debug(f"Input columns: {df.columns}")

        result_df = df.clone()

        # Calculate intrinsic value
        if self.config.intrinsic_value_enabled:
            logger.debug("Calculating intrinsic value...")
            result_df = self._calculate_intrinsic_value(result_df)

            # Calculate premium column first
            logger.debug("Calculating premium...")
            result_df = self._calculate_premium(result_df)
            logger.debug(f"After premium calculation: {len(result_df)} rows")

            # if "premium" not in result_df and "premium" in result_df.columns:
            #     ranking_columns.append("premium")
            logger.debug(f"After intrinsic value: {len(result_df)} rows")
            


        # Calculate ranking indicators (including premium)
        if self.config.rankings_enabled:
            logger.debug("Calculating rankings...")
            # Add premium to ranking columns if not already there
            # ranking_columns = self.config.ranking_columns.copy()
            # # Temporarily update config for this calculation
            # original_columns = self.config.ranking_columns
            # self.config.ranking_columns = ranking_columns
            logger.debug(f"Available columns before _calculate_rankings: {result_df.columns}")

            result_df = self._calculate_rankings(result_df)

            # Restore original config
            # self.config.ranking_columns = original_columns

            logger.debug(f"After rankings: {len(result_df)} rows, added ranking columns")



        # Calculate pattern detection
        if self.config.patterns_enabled:
            logger.debug("Calculating patterns...")
            result_df = self._calculate_patterns(result_df)
            logger.debug(f"After patterns: {len(result_df)} rows")

        # Calculate rise/drop indicators
        logger.debug("Calculating rise/drop indicators...")
        logger.debug(f"Available columns before rise/drop: {result_df.columns}")
        result_df = self._calculate_rise_drop_indicators(result_df)
        logger.debug(f"After rise/drop indicators: {len(result_df)} rows")

        logger.debug("Calculating spark indicators...")
        result_df = self._calculate_spark_indicators(result_df)
        logger.debug(f"After spark indicators: {len(result_df)} rows")
        
        # Calculate trading signals
        if self.config.trading_signals_enabled:
            logger.debug("Calculating trading signals...")
            logger.debug(f"Available columns before trading signals: {result_df.columns}")
            result_df = self._calculate_trading_signals(result_df)
            logger.debug(f"After trading signals: {len(result_df)} rows")

        logger.debug("Custom indicators calculation completed")
        logger.debug(f"Final columns: {result_df.columns}")
        return result_df
    
    def _calculate_rankings(self, df: pl.DataFrame) -> pl.DataFrame:
        """Calculate PERCENTRANK equivalent for specified columns"""
        for column in self.config.ranking_columns:
            if column in df.columns:
                try:
                    rank_col_name = f"{column}_rank"
                    percent_rank_col_name = f"{column}_per_rank"

                    # Get ranking method for this column
                    ranking_method = self.config.ranking_methods.get(column, "min")
                    # descending_order = self.config.ranking_methods.get(column, True)
                    descending_order = True

                    # Special handling for delta_volume - consider only positive values
                    if column == "delta_volume":
                        #Print the top 5 rows of delta_volume. 
                        # Print only the values of column delta_volume
                        # Print the column name and the value
                        # logger.info(f"Top 5 rows of delta_volume before ranking: {df.sort(column, descending=True).head(5)[column]}")
                        df = df.with_columns(
                            pl.when(pl.col(column) > 0)
                            .then(pl.col(column))
                            .otherwise(0)
                            .rank(method=ranking_method, descending=descending_order)
                            .alias(rank_col_name)
                        )


                    # Special handling for delta - use absolute value
                    elif column == "delta":
                        df = df.with_columns(
                            pl.col(column)
                            .abs()
                            .rank(method=ranking_method, descending=descending_order)
                            .alias(rank_col_name)
                        )

                    else:
                        # Standard ranking calculation
                        df = df.with_columns(
                            pl.col(column)
                            .rank(method=ranking_method, descending=descending_order)
                            .alias(rank_col_name)
                        )

                    df = df.with_columns((100 - ((pl.col(rank_col_name).truediv(pl.len())) * 100)).round(2)
                                         .alias(percent_rank_col_name))
                    logger.debug(f"Calculated ranking for column {column} using method {ranking_method}")
                except Exception as e:
                    logger.warning(f"Failed to calculate ranking for column {column}: {e}")

        return df

    def _calculate_premium(self, df: pl.DataFrame) -> pl.DataFrame:
        """Calculate premium column based on option type and intrinsic value"""
        try:
            # Premium = last_price - intrinsic_value
            # For CE: intrinsic_value = max(current_price - strike, 0)
            # For PE: intrinsic_value = max(strike - current_price, 0)

            # df = df.with_columns([
            #     # Calculate intrinsic value for premium calculation
            #     pl.when(
            #         (pl.col("expiry_type") == "CE")
            #     ).then(
            #         pl.max_horizontal([pl.col("current_price") - pl.col("strike"), pl.lit(0)])
            #     ).when(
            #         (pl.col("expiry_type") == "PE")
            #     ).then(
            #         pl.max_horizontal([pl.col("strike") - pl.col("current_price"), pl.lit(0)])
            #     ).otherwise(0).alias("temp_intrinsic_value")
            # ])

            # Calculate premium = last_price - intrinsic_value
            df = df.with_columns([
                (pl.col("last_price") - pl.col("intrinsic_value")).alias("premium")
            ])

            # Remove temporary column
            # df = df.drop("temp_intrinsic_value")

            logger.debug("Calculated premium column")
        except Exception as e:
            logger.warning(f"Failed to calculate premium: {e}")

        return df

    def _calculate_intrinsic_value(self, df: pl.DataFrame) -> pl.DataFrame:
        """Calculate intrinsic value based on Excel formula"""
        try:
            # Excel formula: =IF(H2="CE",IF(E2<D2,ABS(D2-E2),""),IF(E2>D2,ABS(E2-D2),""))
            # H2 = expiry_type, E2 = strike, D2 = current_price
            
            df = df.with_columns([
                pl.when(
                    (pl.col("expiry_type") == "CE") & (pl.col("strike") < pl.col("current_price"))
                ).then(
                    (pl.col("current_price") - pl.col("strike")).abs()
                ).when(
                    (pl.col("expiry_type") == "PE") & (pl.col("strike") > pl.col("current_price"))
                ).then(
                    (pl.col("strike") - pl.col("current_price")).abs()
                ).otherwise(0).alias("intrinsic_value")
            ])
            
            # # Calculate intrinsic value rank using configurable method
            # ranking_method = self.config.ranking_methods.get("intrinsic_value", "min")
            # df = df.with_columns(
            #     pl.col("intrinsic_value")
            #     .rank(method=ranking_method, descending=True)
            #     .alias("intrinsic_value_rank")
            # )

            # df = df.with_columns((100-(pl.col("intrinsic_value_rank").truediv(pl.len())*100))
            #                      .alias("intrinsic_value_per_rank"))
            
            logger.debug("Calculated intrinsic value")
        except Exception as e:
            logger.warning(f"Failed to calculate intrinsic value: {e}")
        
        return df
    
    def _calculate_patterns(self, df: pl.DataFrame) -> pl.DataFrame:
        """Calculate pattern detection indicators"""
        try:
            logger.debug("Starting pattern detection calculations")
            # Sort by timestamp for proper sequence analysis
            df = df.sort("timestamp")

            # Calculate price change percentage for pattern detection
            if "last_price" in df.columns:
                df = df.with_columns([
                    ((pl.col("last_price") - pl.col("last_price").shift(1)) /
                     pl.col("last_price").shift(1) * 100).alias("price_change_pct")
                ])
                logger.debug("Calculated price change percentage")

            # Consecutive Price Drop: =IF(AND(AD3<0,AD2<0),"Yes","")
            # Using price change percentage
            if "price_change_pct" in df.columns:
                df = df.with_columns([
                    (
                        (pl.col("price_change_pct") < 0) &
                        (pl.col("price_change_pct").shift(1) < 0)
                    ).cast(pl.UInt8).alias("consecutive_price_drop")
                ])
                logger.debug("Calculated consecutive price drop")

            # Consecutive OI Build: =IF(AND(AE3>=0,AE2>=0),"Yes","")
            # Calculate OI change
            if "oi" in df.columns:
                df = df.with_columns([
                    (pl.col("oi") - pl.col("oi").shift(1)).alias("oi_change")
                ])
                df = df.with_columns([
                    (
                        (pl.col("oi_change") >= 0) &
                        (pl.col("oi_change").shift(1) >= 0)
                    ).cast(pl.UInt8).alias("consecutive_oi_build")
                ])
                logger.debug("Calculated consecutive OI build")

            # 1 Candle Dip: =IF(AL4<Master!$T$2,"YES","")
            # Using configurable threshold
            candle_dip_threshold = self.config.thresholds.get('candle_dip', -2.0)
            if "price_change_pct" in df.columns:
                df = df.with_columns([
                    (pl.col("price_change_pct") < candle_dip_threshold).cast(pl.UInt8).alias("candle_dip")
                ])
                logger.debug("Calculated candle dip")

            # 1 Candle Rise: =IF(AL4>Master!$U$2,"YES","")
            candle_rise_threshold = self.config.thresholds.get('candle_rise', 2.0)
            if "price_change_pct" in df.columns:
                df = df.with_columns([
                    (pl.col("price_change_pct") > candle_rise_threshold).cast(pl.UInt8).alias("candle_rise")
                ])
                logger.debug("Calculated candle rise")

            logger.debug("Completed pattern detection indicators")
        except Exception as e:
            logger.warning(f"Failed to calculate pattern indicators: {e}")

        return df
    
    def _calculate_rise_drop_indicators(self, df: pl.DataFrame) -> pl.DataFrame:
        """Calculate all rise/drop pattern indicators based on Excel formulas"""
        try:
            logger.debug("Starting rise/drop indicators calculation")
            # Sort by timestamp for proper sequence analysis
            df = df.sort("timestamp")

            # 1 CANDLE DIP: =IF(AL4<Master!$T$2,"YES","")
            if "price_change_pct" in df.columns:
                df = df.with_columns([
                    (pl.col("price_change_pct") < self.thresholds['candle_dip']).cast(pl.UInt8).alias("candle_dip_signal")
                ])
                logger.debug("Calculated candle dip signal")

            # 1 CANDLE RISE: =IF(AL4>Master!$U$2,"YES","")
            if "price_change_pct" in df.columns:
                df = df.with_columns([
                    (pl.col("price_change_pct") > self.thresholds['candle_rise']).cast(pl.UInt8).alias("candle_rise_signal")
                ])
                logger.debug("Calculated candle rise signal")

            # INTS VALUE RISE: =IF(AND(AR4>Master!$G$2,AR3>Master!$G$3,AR2>Master!$G$4),"YES","")
            if "intrinsic_value_rank" in df.columns:
                df = df.with_columns([
                    (
                        (pl.col("intrinsic_value_per_rank") > self.thresholds['int_value_rise_std'][0]) &
                        (pl.col("intrinsic_value_per_rank").shift(1) > self.thresholds['int_value_rise_std'][1])
                    ).cast(pl.UInt8).alias("ints_value_rise_std")
                ])
                logger.debug("Calculated intrinsic value rise std")

                df = df.with_columns([
                    (
                            (pl.col("intrinsic_value_per_rank") > self.thresholds['int_value_rise_ultra'][0]) &
                            (pl.col("intrinsic_value_per_rank").shift(1) > self.thresholds['int_value_rise_ultra'][1])
                    ).cast(pl.UInt8).alias("ints_value_rise_ultra")
                ])
                logger.debug("Calculated intrinsic value rise ultra")

            # INTS VALUE DROP: =IF(AND(AR4<Master!$H$2,AR3<Master!$H$3,AR2<Master!$H$4),"YES","")
            if "intrinsic_value_rank" in df.columns:
                df = df.with_columns([
                    (
                        (pl.col("intrinsic_value_per_rank") < self.thresholds['int_value_drop'][0]) &
                        (pl.col("intrinsic_value_per_rank").shift(1) < self.thresholds['int_value_drop'][1])
                    ).cast(pl.UInt8).alias("ints_value_drop")
                ])
                logger.debug("Calculated intrinsic value drop")

            # PREMIUM RISE: =IF(AND(AT4>Master!$I$2,AT3>Master!$I$3,AT2>Master!$I$4),"YES","")
            # Premium rank should already be calculated by the ranking system

            if "premium_rank" in df.columns:
                df = df.with_columns([
                    (
                        (pl.col("premium_per_rank") > self.thresholds['premium_rise'][0]) &
                        (pl.col("premium_per_rank").shift(1) > self.thresholds['premium_rise'][1]) 
                    ).cast(pl.UInt8).alias("premium_rise")
                ])
                logger.debug("Calculated premium rise")

            # PREMIUM DROP: =IF(AND(AT4<Master!$J$2,AT3<Master!$J$3,AT2<Master!$J$4),"YES","")
            if "premium_rank" in df.columns:
                df = df.with_columns([
                    (
                        (pl.col("premium_per_rank") < self.thresholds['premium_drop'][0]) &
                        (pl.col("premium_per_rank").shift(1) < self.thresholds['premium_drop'][1]) 
                    ).cast(pl.UInt8).alias("premium_drop")
                ])
                logger.debug("Calculated premium drop")

            # GAMMA RISE: =IF(AND(AV4>Master!$K$2,AV3>Master!$K$3,AV2>Master!$K$4,AV2<Master!$K$2),"YES","")
            if "gamma_rank" in df.columns:
                df = df.with_columns([
                    (
                        (pl.col("gamma_per_rank") > self.thresholds['gamma_rise'][0]) &
                        (pl.col("gamma_per_rank").shift(1) > self.thresholds['gamma_rise'][1])
                    ).cast(pl.UInt8).alias("gamma_rise")
                ])
                logger.debug("Calculated gamma rise")

            # DEL VOL RISE: =IF(AND(AX4>Master!$L$2,AX3>Master!$L$3,AX2>Master!$L$4),"YES","")
            # Use volume rank as proxy for del_vol_rank
            if "delta_volume_rank" in df.columns:
                df = df.with_columns([
                    (
                        (pl.col("delta_volume_per_rank") > self.thresholds['del_vol_rise'][0]) &
                        (pl.col("delta_volume_per_rank").shift(1) > self.thresholds['del_vol_rise'][1])
                    ).cast(pl.UInt8).alias("del_vol_rise")
                ])
                logger.debug("Calculated del vol rise")

            # # IV Double RISE: =IF(AND(AZ4>Master!$M$2,AZ3>Master!$M$3),"YES","")
            # if "iv_rank" in df.columns:
            #     df = df.with_columns([
            #         (
            #             (pl.col("iv_per_rank") > self.thresholds['iv_double_rise'][0]) &
            #             (pl.col("iv_per_rank").shift(1) > self.thresholds['iv_double_rise'][1])
            #         ).cast(pl.UInt8).alias("iv_double_rise")
            #     ])
            #     logger.debug("Calculated IV double rise")

            # # IV Triple RISE: =IF(AND(AZ4>Master!$M$2,AZ3>Master!$M$3,AZ2>Master!$M$4,AZ2<Master!$M$2),"YES","")
            # if "iv_rank" in df.columns:
            #     df = df.with_columns([
            #         (
            #             (pl.col("iv_per_rank") > self.thresholds['iv_triple_rise'][0]) &
            #             (pl.col("iv_per_rank").shift(1) > self.thresholds['iv_triple_rise'][1]) &
            #             (pl.col("iv_per_rank").shift(2) > self.thresholds['iv_triple_rise'][2]) &
            #             (pl.col("iv_per_rank").shift(2) < self.thresholds['iv_triple_rise'][0])
            #         ).cast(pl.UInt8).alias("iv_triple_rise")
            #     ])
            #     logger.debug("Calculated IV triple rise")

            # # IV DROP: =IF(AND(AZ4<Master!$N$2,AZ3<Master!$N$3,AZ2<Master!$N$4,AZ2>Master!$N$2),"YES","")
            # if "iv_rank" in df.columns:
            #     df = df.with_columns([
            #         (
            #             (pl.col("iv_per_rank") < self.thresholds['iv_drop'][0]) &
            #             (pl.col("iv_per_rank").shift(1) < self.thresholds['iv_drop'][1]) &
            #             (pl.col("iv_per_rank").shift(2) < self.thresholds['iv_drop'][2]) &
            #             (pl.col("iv_per_rank").shift(2) > self.thresholds['iv_drop'][0])
            #         ).cast(pl.UInt8).alias("iv_drop")
            #     ])
            #     logger.debug("Calculated IV drop")

            # # DELTA RISE: =IF(AND(BD4>Master!$O$2,BD3>Master!$O$3,BD2>Master!$O$4),"YES","")
            # if "delta_rank" in df.columns:
            #     df = df.with_columns([
            #         (
            #             (pl.col("delta_per_rank") > self.thresholds['delta_rise'][0]) &
            #             (pl.col("delta_per_rank").shift(1) > self.thresholds['delta_rise'][1]) &
            #             (pl.col("delta_per_rank").shift(2) > self.thresholds['delta_rise'][2])
            #         ).cast(pl.UInt8).alias("delta_rise")
            #     ])
            #     logger.debug("Calculated delta rise")

            # # DELTA DROP: =IF(AND(BD4<Master!$P$2,BD3<Master!$P$3,BD2<Master!$P$4),"YES","")
            # if "delta_rank" in df.columns:
            #     df = df.with_columns([
            #         (
            #             (pl.col("delta_per_rank") < self.thresholds['delta_drop'][0]) &
            #             (pl.col("delta_per_rank").shift(1) < self.thresholds['delta_drop'][1]) &
            #             (pl.col("delta_per_rank").shift(2) < self.thresholds['delta_drop'][2])
            #         ).cast(pl.UInt8).alias("delta_drop")
            #     ])
            #     logger.debug("Calculated delta drop")

            # # THETA RISE: =IF(AND(BF4>Master!$Q$2,BF3>Master!$Q$3,BF2>Master!$Q$4),"YES","")
            # if "theta_rank" in df.columns:
            #     df = df.with_columns([
            #         (
            #             (pl.col("theta_per_rank") > self.thresholds['theta_rise'][0]) &
            #             (pl.col("theta_per_rank").shift(1) > self.thresholds['theta_rise'][1]) &
            #             (pl.col("theta_per_rank").shift(2) > self.thresholds['theta_rise'][2])
            #         ).cast(pl.UInt8).alias("theta_rise")
            #     ])
            #     logger.debug("Calculated theta rise")

            # # THETA DROP: =IF(AND(BF4<Master!$R$2,BF3<Master!$R$3,BF2<Master!$R$4),"YES","")
            # if "theta_rank" in df.columns:
            #     df = df.with_columns([
            #         (
            #             (pl.col("theta_per_rank") < self.thresholds['theta_drop'][0]) &
            #             (pl.col("theta_per_rank").shift(1) < self.thresholds['theta_drop'][1]) &
            #             (pl.col("theta_per_rank").shift(2) < self.thresholds['theta_drop'][2])
            #         ).cast(pl.UInt8).alias("theta_drop")
            #     ])
            #     logger.debug("Calculated theta drop")

            # OI Rise: =IF(AND(AK4>Master!$V$2,AK3>Master!$V$3,AK2>Master!$V$4),"YES","")
            if "oi_rank" in df.columns:
                df = df.with_columns([
                    (
                        (pl.col("oi_per_rank") > self.thresholds['oi_rise'][0]) &
                        (pl.col("oi_per_rank").shift(1) > self.thresholds['oi_rise'][1]) 
                    ).cast(pl.UInt8).alias("oi_rise")
                ])
                logger.debug("Calculated OI rise")

                df = df.with_columns([
                    (
                        (pl.col("oi_per_rank") < self.thresholds['oi_drop'][0]) &
                        (pl.col("oi_per_rank").shift(1) < self.thresholds['oi_drop'][1])                        
                    ).cast(pl.UInt8).alias("oi_drop")
                ])
                logger.debug("Calculated OI drop")

            # # VEGA COOL: =IF(AND(BB4<=Master!$S$2,BB3<Master!$S$3,BB2<Master!$S$4,BB2>Master!$S$2),"YES","")
            # if "vega_rank" in df.columns:
            #     df = df.with_columns([
            #         (
            #             (pl.col("vega_per_rank") <= self.thresholds['vega_cool'][0]) &
            #             (pl.col("vega_per_rank").shift(1) < self.thresholds['vega_cool'][1]) &
            #             (pl.col("vega_per_rank").shift(2) < self.thresholds['vega_cool'][2]) &
            #             (pl.col("vega_per_rank").shift(2) > self.thresholds['vega_cool'][0])
            #         ).cast(pl.UInt8).alias("vega_cool")
            #     ])
            #     logger.debug("Calculated vega cool")

            # # (GAMMA MOVE): =IF(BM4="YES","GAMMA MOVE","")
            # if "gamma_rise" in df.columns:
            #     df = df.with_columns([
            #         pl.when(pl.col("gamma_rise") == 1)
            #         .then(pl.lit("GAMMA MOVE")).otherwise(pl.lit("")).alias("gamma_move")
            #     ])
            #     logger.debug("Calculated gamma move")

            logger.debug("Completed rise/drop indicators calculation")
            return df

        except Exception as e:
            logger.warning(f"Failed to calculate rise/drop indicators: {e}")
            return df

    def _calculate_spark_indicators(self, df: pl.DataFrame) -> pl.DataFrame:
        """Calculate all spark indicators based on Excel formulas"""
        try:
            logger.debug("Starting spark indicators calculation")
            # Sort by timestamp for proper sequence analysis
            df = df.sort("timestamp")

            if "intrinsic_value_per_rank" in df.columns:
                df = df.with_columns([
                    pl.col("intrinsic_value_per_rank").pct_change()
                    .round(2)
                    .alias("int_value_spark")
                ])
                logger.debug("Calculated intrinsic value spark")

            if "delta_per_rank" in df.columns:
                df = df.with_columns([
                    pl.col("delta_per_rank").pct_change()
                    .round(2)
                    .alias("delta_spark")
                ])
                logger.debug("Calculated delta spark")

            if "delta_volume_per_rank" in df.columns:
                df = df.with_columns([
                    pl.col("delta_volume_per_rank").pct_change()
                    .round(2)
                    .alias("delta_volume_spark")
                    
                ])
                #print the top 5 rows of delta_volume_spark & delta_volume_per_rank
                logger.debug(f"Top 5 rows of delta_volume_spark: {df.head(5)[['timestamp', 'delta_volume_spark', 'delta_volume_per_rank']]}")

                logger.debug("Calculated delta volume spark")    

            if "iv_per_rank" in df.columns:
                df = df.with_columns([
                    pl.col("iv_per_rank").pct_change()
                    .round(2)
                    .alias("iv_spark")
                ])
                logger.debug("Calculated IV spark")

            if "vega_per_rank" in df.columns:
                df = df.with_columns([
                    (pl.col("vega_per_rank").pct_change().round(2))
                    .alias("vega_spark")                    
                ])
                logger.debug("Calculated vega spark")

            if "gamma_per_rank" in df.columns:
                df = df.with_columns([
                    (pl.col("gamma_per_rank").pct_change().round(2))
                    .alias("gamma_spark")
                ])
                logger.debug("Calculated GAMMA spark")

            logger.debug("Completed spark indicators calculation")
            return df

        except Exception as e:
            logger.warning(f"Failed to calculate spark indicators: {e}")
            return df
        

    def _calculate_trading_signals(self, df: pl.DataFrame) -> pl.DataFrame:
        """Calculate trading signals based on Excel formulas"""
        try:
            logger.debug("Starting trading signals calculation")
            logger.debug(f"Available columns: {df.columns}")

            int_value_spark_threhold = self.thresholds['int_value_spark']
            delta_spark_threshold = self.thresholds['delta_spark']
            iv_spark_threshold = self.thresholds['iv_spark']
            vega_spark_threshold = self.thresholds['vega_spark']
            gamma_spark_threshold = self.thresholds['gamma_spark']

            df = df.with_columns([
                    pl.when((pl.col("int_value_spark") > int_value_spark_threhold) 
                            & (pl.col("delta_spark") > delta_spark_threshold))
                    .then(pl.lit("BUY2"))
                    .when((pl.col("int_value_spark") > int_value_spark_threhold) 
                          & (pl.col("delta_spark") < delta_spark_threshold))
                    .then(pl.lit("BUY1"))
                    .when((pl.col("int_value_spark") < int_value_spark_threhold) 
                          & (pl.col("delta_spark") > delta_spark_threshold))
                    .then(pl.lit("BUY0.5"))
                    .otherwise(pl.lit(""))
                    .alias("buy_signal")
            ])
            logger.debug("Calculated buy_primary")

            df = df.with_columns([
                (pl.col("int_value_spark") + pl.col("delta_spark"))
                .alias("buy_weight")
            ])


            df = df.with_columns(
                    pl.when(
                        (pl.col("iv_spark") > iv_spark_threshold)
                        & (pl.col("vega_spark") > vega_spark_threshold)
                        & (pl.col("gamma_spark") > gamma_spark_threshold)
                    ).then(pl.lit("BUY3"))
                    .when(
                        (pl.col("iv_spark") > iv_spark_threshold)
                        & (pl.col("vega_spark") > vega_spark_threshold)
                        & (pl.col("gamma_spark") < gamma_spark_threshold)
                    ).then(pl.lit("BUY2"))
                    .when(
                        (pl.col("iv_spark") < iv_spark_threshold)
                        & (pl.col("vega_spark") > vega_spark_threshold)
                        & (pl.col("gamma_spark") > gamma_spark_threshold)
                    ).then(pl.lit("BUY2"))
                    .when(
                        (pl.col("iv_spark") > iv_spark_threshold)
                        & (pl.col("vega_spark") < vega_spark_threshold)
                        & (pl.col("gamma_spark") > gamma_spark_threshold)
                    ).then(pl.lit("BUY2"))
                    .when(
                        (pl.col("iv_spark") > iv_spark_threshold)
                        & (pl.col("vega_spark") < vega_spark_threshold)
                        & (pl.col("gamma_spark") < gamma_spark_threshold)
                    ).then(pl.lit("BUY1"))
                    .when(
                        (pl.col("iv_spark") < iv_spark_threshold)
                        & (pl.col("vega_spark") < vega_spark_threshold)
                        & (pl.col("gamma_spark") > gamma_spark_threshold)
                    ).then(pl.lit("BUY1"))
                    .when(
                        (pl.col("iv_spark") < iv_spark_threshold)
                        & (pl.col("vega_spark") > vega_spark_threshold)
                        & (pl.col("gamma_spark") < gamma_spark_threshold)
                    ).then(pl.lit("BUY1"))
                    .otherwise(pl.lit(""))
                    .alias("buy_special")
                )
            
            df = df.with_columns(
                    pl.when(
                        (pl.col("iv_spark") > iv_spark_threshold)
                        & (pl.col("vega_spark") > vega_spark_threshold)
                        & (pl.col("gamma_spark") > gamma_spark_threshold)
                    ).then(pl.lit("IV + VEGA + GAMMA"))
                    .when(
                        (pl.col("iv_spark") > iv_spark_threshold)
                        & (pl.col("vega_spark") > vega_spark_threshold)
                        & (pl.col("gamma_spark") < gamma_spark_threshold)
                    ).then(pl.lit("IV + VEGA"))
                    .when(
                        (pl.col("iv_spark") < iv_spark_threshold)
                        & (pl.col("vega_spark") > vega_spark_threshold)
                        & (pl.col("gamma_spark") > gamma_spark_threshold)
                    ).then(pl.lit("VEGA + GAMMA"))
                    .when(
                        (pl.col("iv_spark") > iv_spark_threshold)
                        & (pl.col("vega_spark") < vega_spark_threshold)
                        & (pl.col("gamma_spark") > gamma_spark_threshold)
                    ).then(pl.lit("IV + GAMMA"))
                    .when(
                        (pl.col("iv_spark") > iv_spark_threshold)
                        & (pl.col("vega_spark") < vega_spark_threshold)
                        & (pl.col("gamma_spark") < gamma_spark_threshold)
                    ).then(pl.lit("IV only"))
                    .when(
                        (pl.col("iv_spark") < iv_spark_threshold)
                        & (pl.col("vega_spark") < vega_spark_threshold)
                        & (pl.col("gamma_spark") > gamma_spark_threshold)
                    ).then(pl.lit("GAMMA only"))
                    .when(
                        (pl.col("iv_spark") < iv_spark_threshold)
                        & (pl.col("vega_spark") > vega_spark_threshold)
                        & (pl.col("gamma_spark") < gamma_spark_threshold)
                    ).then(pl.lit("VEGA only"))
                    .otherwise(pl.lit(""))
                    .alias("buy_special_desc")
                )
            
            df = df.with_columns(
                pl.when((pl.col("oi_drop") == 1) & (pl.col("del_vol_rise") == 1))
                .then(pl.lit("BUY2"))
                .when((pl.col("oi_drop") == 1) & (pl.col("del_vol_rise") == 0))
                .then(pl.lit("BUY1"))
                .when((pl.col("oi_drop") == 0) & (pl.col("del_vol_rise") == 1))
                .then(pl.lit("BUY1"))
                .otherwise(pl.lit(""))
                .alias("short_covering_move")
            )

            df = df.with_columns(
                pl.when((pl.col("oi_drop") + pl.col("del_vol_rise")) == 2)
                .then(pl.lit("OI+VOL"))
                .when((pl.col("oi_drop") == 1))
                .then(pl.lit("OI only"))
                .when((pl.col("del_vol_rise") == 1))
                .then(pl.lit("Vol only"))
                .otherwise(pl.lit(""))
                .alias("short_covering_desc")
            )

            logger.debug("Completed trading signals calculation")
            return df

        except Exception as e:
            logger.warning(f"Failed to calculate trading signals: {e}")
            return df
    
    def calculate_for_partition(self, df: pl.DataFrame, partition_key: str) -> pl.DataFrame:
        """Calculate custom indicators for a specific partition"""
        if df.is_empty():
            return df
        
        logger.debug(f"Calculating custom indicators for partition: {partition_key}")
        
        # Sort by timestamp to ensure proper time series order
        df = df.sort("timestamp")
        
        # Calculate indicators
        result_df = self.calculate_all(df)
        
        return result_df
    
    def get_indicator_columns(self) -> List[str]:
        """Get list of all custom indicator column names that will be generated from config"""
        # Get all custom indicator columns from output schema config
        config = OutputSchemaManager.load_output_columns_config()
        if not config or 'columns' not in config:
            # Fallback to hardcoded logic if config not available
            return self._get_hardcoded_indicator_columns()

        custom_columns = []
        for column_name, column_config in config['columns'].items():
            category = column_config.get('category', '')
            if category in ['custom_indicators', 'ranking_indicators', 'pattern_detection', 'trading_signals']:
                custom_columns.append(column_name)

        return custom_columns

    def _get_hardcoded_indicator_columns(self) -> List[str]:
        """Fallback method with hardcoded logic"""
        columns = []

        # Ranking columns
        if self.config.rankings_enabled:
            for column in self.config.ranking_columns:
                columns.append(f"{column}_rank")

        # Intrinsic value columns
        if self.config.intrinsic_value_enabled:
            columns.extend(["intrinsic_value", "intrinsic_value_rank"])

        # Pattern detection columns
        if self.config.patterns_enabled:
            columns.extend([
                "consecutive_price_drop",
                "consecutive_oi_build",
                "candle_dip",
                "candle_rise"
            ])

        # Premium column (always calculated)
        columns.append("premium")

        # Rise/drop indicators (always calculated)
        columns.extend([
            "candle_dip_signal", "candle_rise_signal",
            "ints_value_rise_std", "ints_value_rise_ultra", "ints_value_drop",
            "premium_rise", "premium_drop", "premium_rank",
            "gamma_rise", "del_vol_rise",
            "iv_double_rise", "iv_triple_rise", "iv_drop",
            "delta_rise", "delta_drop",
            "theta_rise", "theta_drop",
            "oi_rise", "vega_cool", "gamma_move"
        ])

        # Trading signal columns
        if self.config.trading_signals_enabled:
            columns.extend([
                "buy_signal_1", "buy_signal_2", "buy_signal_3", "buy_signal_4", "buy_signal_5",
                "sell_signal_1", "sell_signal_2", "sell_signal_3", "sell_signal_4", "sell_signal_5",
                "buy_with_iv_vega", "buy_with_gamma",
                "sell_with_iv", "gamma_present"
            ])

        return columns
